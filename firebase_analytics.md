
# Firebase Analytics Specification

This document lists all the required Firebase Analytics events, user properties, and tracking details for the Flutter Web project.

---

## 📌 User Properties

| Property Name      | Parameter         | Example Value        |
|--------------------|-------------------|-----------------------|
| User Role/Type     | `user_role`       | `vendor`              |
| Location of User   | `user_location`   | `mumbai`              |
| Account Created At | `user_created_date` | `18 July 2025`      |
| KYC Status         | `kyc_status`      | `verified` / `pending` / `rejected` |

---

## 📄 Page View Tracking

- All page views must be tracked whenever the path changes.
- Note: The address bar is not updating correctly, which may affect trigger implementation.

---

## 🔐 Login & Logout Events

| Event Name     | Trigger                      | Parameters                         |
|----------------|------------------------------|-------------------------------------|
| `login_started`| Landed on login page         | `source = "direct"` or `utm_*`     |
| `otp_requested`| Requested for OTP            | `error` (if any)                   |
| `otp_verified` | Entered OTP, pressed login   | `status = "success"`, `latency`    |
| `otp_failed`   | Incorrect OTP submitted      | `status = "failed"`, `reason`      |
| `otp_resent`   | Clicked on resend OTP        | –                                   |
| `user_logout`  | Clicked logout               | `screen = "<screen_name>"`         |
| `logout`       | Clicked logout               | `source_screen`                    |
| `help`         | Clicked help                 | `source_screen`                    |

---

## 🌐 Navigation Events

| Event Name               | Trigger                       | Parameters                           |
|--------------------------|-------------------------------|---------------------------------------|
| `left_navigation_clicked`| Click on left nav panel       | –                                     |
| `tab_selected`           | Click on tab (drafts/classes) | `tab`, `section`                      |
| `notification_icon_clicked` | Notification icon clicked | –                                     |

---

## 🎟️ Event Creation

| Event Name                 | Trigger                                 | Parameters                                |
|----------------------------|------------------------------------------|--------------------------------------------|
| `event_create_clicked`     | Clicked create event                     | –                                          |
| `event_creation_started`   | Create event form loaded                 | `latency`, `status`                        |
| `event_form_field_interacted` | Interacted with event form fields   | `field_name = <field>`                     |
| `event_save_draft`         | Clicked save as draft                   | `draft_id`                                 |
| `event_discard_changes`    | Left without saving                     | –                                          |
| `event_submit_review`      | Submitted for review                    | `draft_id`                                 |
| `event_form_field_add_new_address` | Clicked add new address        | –                                          |
| `event_submission_rejected`| Admin rejected event (red banner)       | `draft_id`                                 |
| `event_published`          | Admin approved and published            | `event_id`                                 |
| `event_unpublished`        | User unpublished                        | `event_id`                                 |
| `event_deleted`            | User deleted event                      | `event_id` or `draft_id`                   |
| `event_duplicated`         | User duplicated event                   | `event_id` or `draft_id`                   |
| `event_details_viewed`     | Viewed draft/published event details    | `event_id` or `draft_id`                   |
| `add_new_address`          | Clicked "Add New Address"               | –                                          |

---

## 🏫 Class Creation

| Event Name                    | Trigger                            | Parameters                                |
|-------------------------------|-------------------------------------|--------------------------------------------|
| `class_create_clicked`        | Clicked create class                | –                                          |
| `class_creation_started`      | Create class form loaded            | `latency`, `status`                        |
| `class_form_field_interacted` | Interacted with class form fields  | `field_name = <field>`                     |
| `class_save_draft`            | Clicked save as draft              | `draft_id`                                 |
| `class_discard_changes`       | Left without saving                | –                                          |
| `class_submit_review`         | Submitted for review               | `draft_id`                                 |
| `class_form_field_add_new_address` | Add new address             | –                                          |
| `class_submission_rejected`   | Admin rejected class               | `draft_id`                                 |
| `class_published`             | Admin approved and published       | `class_id`                                 |
| `class_unpublished`           | User unpublished                   | `class_id`                                 |
| `class_deleted`               | User deleted class                 | `class_id` or `draft_id`                   |
| `class_duplicated`            | User duplicated class              | `class_id` or `draft_id`                   |
| `class_details_viewed`        | Viewed draft/published class       | `class_id` or `draft_id`                   |

---

## 🧑‍🤝‍🧑 Invite Events

| Event Name                         | Trigger                      |
|------------------------------------|-------------------------------|
| `invite_parent_copied`             | Clicked copy parent invite   |
| `invite_parent_whatsapp_clicked`   | Clicked invite parent (WA)   |
| `invite_business_copied`           | Clicked copy business invite |
| `invite_business_whatsapp_clicked` | Clicked invite business (WA) |

---

## 👤 Profile Events

| Event Name                     | Trigger                           | Parameters                        |
|--------------------------------|------------------------------------|------------------------------------|
| `profile_discard_changes`      | Left without saving profile       | –                                  |
| `profile_form_field_interacted`| Interacted with profile field     | `field_name = what_business_does` |
| `profile_details_updated`      | Saved profile details             | –                                  |

---

## 📍 Address Events

| Event Name     | Trigger                      |
|----------------|-------------------------------|
| `new_address`  | Clicked new address           |
| `save_address` | Saved address                 |
| `edit_address` | Edited existing address       |
| `delete_address`| Deleted address              |

---

## ⚠️ Error Events

| Event Name             | Trigger                          | Parameters                          |
|------------------------|-----------------------------------|--------------------------------------|
| `form_validation_error`| Any inline form error            | `form_name`, `field_name`, `error_message` |
| `api_error`            | Backend error encountered        | `error_code`, `source_page`          |
| `toast_error`          | Toast error message              | `error_message`, `source_page`       |

---

*End of document.*
