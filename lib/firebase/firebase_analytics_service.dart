import 'dart:developer';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

/// Firebase Analytics Service for tracking events and user properties
/// according to the requirements specified in firebase_analytics.md
class FirebaseAnalyticsService {
  static final FirebaseAnalyticsService _instance = FirebaseAnalyticsService._internal();
  late FirebaseAnalytics _analytics;
  late FirebaseAnalyticsObserver _observer;
  bool _isInitialized = false;

  factory FirebaseAnalyticsService() {
    return _instance;
  }

  FirebaseAnalyticsService._internal();

  /// Initialize Firebase Analytics
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _analytics = FirebaseAnalytics.instance;
      _observer = FirebaseAnalyticsObserver(analytics: _analytics);
      _isInitialized = true;
      log("Firebase Analytics initialized successfully");
    } catch (e, stackTrace) {
      log("Error initializing Firebase Analytics: $e\n$stackTrace");
    }
  }

  /// Get the analytics observer for navigation tracking
  FirebaseAnalyticsObserver get observer => _observer;

  /// Ensure analytics is initialized before use
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // ==================== USER PROPERTIES ====================

  /// Set user properties as specified in requirements
  Future<void> setUserProperties({
    String? userRole,
    String? userLocation,
    String? userCreatedDate,
    String? kycStatus,
  }) async {
    await _ensureInitialized();
    
    try {
      if (userRole != null) {
        await _analytics.setUserProperty(name: 'user_role', value: userRole);
      }
      if (userLocation != null) {
        await _analytics.setUserProperty(name: 'user_location', value: userLocation);
      }
      if (userCreatedDate != null) {
        await _analytics.setUserProperty(name: 'user_created_date', value: userCreatedDate);
      }
      if (kycStatus != null) {
        await _analytics.setUserProperty(name: 'kyc_status', value: kycStatus);
      }
      
      if (kDebugMode) {
        print("User properties set: role=$userRole, location=$userLocation, created=$userCreatedDate, kyc=$kycStatus");
      }
    } catch (e) {
      log("Error setting user properties: $e");
    }
  }

  /// Set user ID for analytics
  Future<void> setUserId(String userId) async {
    await _ensureInitialized();
    
    try {
      await _analytics.setUserId(id: userId);
      if (kDebugMode) {
        print("User ID set: $userId");
      }
    } catch (e) {
      log("Error setting user ID: $e");
    }
  }

  // ==================== PAGE VIEW TRACKING ====================

  /// Track page views manually (for cases where observer doesn't work)
  Future<void> trackPageView({
    required String pageName,
    String? pageClass,
    Map<String, dynamic>? parameters,
  }) async {
    await _ensureInitialized();
    
    try {
      await _analytics.logScreenView(
        screenName: pageName,
        screenClass: pageClass ?? pageName,
        parameters: parameters,
      );
      
      if (kDebugMode) {
        print("Page view tracked: $pageName");
      }
    } catch (e) {
      log("Error tracking page view: $e");
    }
  }

  // ==================== LOGIN & LOGOUT EVENTS ====================

  /// Track login started event
  Future<void> trackLoginStarted({String source = "direct"}) async {
    await _trackEvent('login_started', {'source': source});
  }

  /// Track OTP requested event
  Future<void> trackOtpRequested({String? error}) async {
    Map<String, dynamic> parameters = {};
    if (error != null) parameters['error'] = error;
    await _trackEvent('otp_requested', parameters);
  }

  /// Track OTP verified event
  Future<void> trackOtpVerified({
    required String status,
    int? latency,
  }) async {
    Map<String, dynamic> parameters = {'status': status};
    if (latency != null) parameters['latency'] = latency;
    await _trackEvent('otp_verified', parameters);
  }

  /// Track OTP failed event
  Future<void> trackOtpFailed({
    required String status,
    String? reason,
  }) async {
    Map<String, dynamic> parameters = {'status': status};
    if (reason != null) parameters['reason'] = reason;
    await _trackEvent('otp_failed', parameters);
  }

  /// Track OTP resent event
  Future<void> trackOtpResent() async {
    await _trackEvent('otp_resent', {});
  }

  /// Track user logout event
  Future<void> trackUserLogout({required String screen}) async {
    await _trackEvent('user_logout', {'screen': screen});
  }

  /// Track logout event
  Future<void> trackLogout({required String sourceScreen}) async {
    await _trackEvent('logout', {'source_screen': sourceScreen});
  }

  /// Track help clicked event
  Future<void> trackHelp({required String sourceScreen}) async {
    await _trackEvent('help', {'source_screen': sourceScreen});
  }

  // ==================== NAVIGATION EVENTS ====================

  /// Track left navigation clicked
  Future<void> trackLeftNavigationClicked() async {
    await _trackEvent('left_navigation_clicked', {});
  }

  /// Track tab selected
  Future<void> trackTabSelected({
    required String tab,
    required String section,
  }) async {
    await _trackEvent('tab_selected', {
      'tab': tab,
      'section': section,
    });
  }

  /// Track notification icon clicked
  Future<void> trackNotificationIconClicked() async {
    await _trackEvent('notification_icon_clicked', {});
  }

  // ==================== EVENT CREATION ====================

  /// Track event create clicked
  Future<void> trackEventCreateClicked() async {
    await _trackEvent('event_create_clicked', {});
  }

  /// Track event creation started
  Future<void> trackEventCreationStarted({
    int? latency,
    String? status,
  }) async {
    Map<String, dynamic> parameters = {};
    if (latency != null) parameters['latency'] = latency;
    if (status != null) parameters['status'] = status;
    await _trackEvent('event_creation_started', parameters);
  }

  /// Track event form field interaction
  Future<void> trackEventFormFieldInteracted({required String fieldName}) async {
    await _trackEvent('event_form_field_interacted', {'field_name': fieldName});
  }

  /// Track event save draft
  Future<void> trackEventSaveDraft({required String draftId}) async {
    await _trackEvent('event_save_draft', {'draft_id': draftId});
  }

  /// Track event discard changes
  Future<void> trackEventDiscardChanges() async {
    await _trackEvent('event_discard_changes', {});
  }

  /// Track event submit for review
  Future<void> trackEventSubmitReview({required String draftId}) async {
    await _trackEvent('event_submit_review', {'draft_id': draftId});
  }

  /// Track add new address from event form
  Future<void> trackEventFormFieldAddNewAddress() async {
    await _trackEvent('event_form_field_add_new_address', {});
  }

  /// Track event submission rejected
  Future<void> trackEventSubmissionRejected({required String draftId}) async {
    await _trackEvent('event_submission_rejected', {'draft_id': draftId});
  }

  /// Track event published
  Future<void> trackEventPublished({required String eventId}) async {
    await _trackEvent('event_published', {'event_id': eventId});
  }

  /// Track event unpublished
  Future<void> trackEventUnpublished({required String eventId}) async {
    await _trackEvent('event_unpublished', {'event_id': eventId});
  }

  /// Track event deleted
  Future<void> trackEventDeleted({String? eventId, String? draftId}) async {
    Map<String, dynamic> parameters = {};
    if (eventId != null) parameters['event_id'] = eventId;
    if (draftId != null) parameters['draft_id'] = draftId;
    await _trackEvent('event_deleted', parameters);
  }

  /// Track event duplicated
  Future<void> trackEventDuplicated({String? eventId, String? draftId}) async {
    Map<String, dynamic> parameters = {};
    if (eventId != null) parameters['event_id'] = eventId;
    if (draftId != null) parameters['draft_id'] = draftId;
    await _trackEvent('event_duplicated', parameters);
  }

  /// Track event details viewed
  Future<void> trackEventDetailsViewed({String? eventId, String? draftId}) async {
    Map<String, dynamic> parameters = {};
    if (eventId != null) parameters['event_id'] = eventId;
    if (draftId != null) parameters['draft_id'] = draftId;
    await _trackEvent('event_details_viewed', parameters);
  }

  /// Track add new address
  Future<void> trackAddNewAddress() async {
    await _trackEvent('add_new_address', {});
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /// Internal method to track events with error handling
  Future<void> _trackEvent(String eventName, Map<String, dynamic> parameters) async {
    await _ensureInitialized();
    
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: parameters.isNotEmpty ? parameters : null,
      );
      
      if (kDebugMode) {
        print("Analytics event tracked: $eventName with parameters: $parameters");
      }
    } catch (e) {
      log("Error tracking event $eventName: $e");
    }
  }
}
